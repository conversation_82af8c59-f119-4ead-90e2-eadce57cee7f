# Search API

## Overview

The Search API provides full-text search functionality across manga titles, alternative titles, and descriptions using PostgreSQL's advanced text search capabilities.

## Endpoints

### Search Manga

**GET** `/api/search`

Search for manga using full-text search with ranking and filtering options.

#### Query Parameters
- `q` (string, required) - Search query
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 100) - Items per page
- `genre` (string, optional) - Filter by genre slug
- `status` (string, optional) - Filter by manga status
  - `ongoing` - Currently publishing
  - `completed` - Finished series
  - `hiatus` - On break

#### Search Features
- **Full-text search** across titles, alternative titles, and descriptions
- **Relevance ranking** using PostgreSQL's `ts_rank` function
- **Multi-word queries** with automatic word stemming
- **Partial word matching** using prefix matching (`word:*`)
- **Genre and status filtering** combined with text search

#### Example Request
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=pirate+adventure&page=1&limit=10&status=ongoing"
```

#### Success Response (200)
```json
{
  "comics": [
    {
      "id": 1,
      "title": "One Piece",
      "slug": "one-piece",
      "alternative_titles": ["ワンピース", "Wan Pīsu"],
      "description": "Gol D. Roger was known as the Pirate King, the strongest and most infamous being to have sailed the Grand Line...",
      "cover_image_url": "https://example.com/covers/one-piece.jpg",
      "status": "ongoing",
      "release_date": "1997-07-22T00:00:00Z",
      "country_of_origin": "Japan",
      "age_rating": "T",
      "total_views": 1500000,
      "total_favorites": 25000,
      "last_chapter_uploaded_at": "2024-01-15T10:30:00Z",
      "uploader_id": 1,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "rank": 0.95,
      "_chapterCount": 1100,
      "authors": [
        {
          "id": 1,
          "name": "Eiichiro Oda",
          "slug": "eiichiro-oda"
        }
      ],
      "genres": [
        {
          "id": 1,
          "name": "Action",
          "slug": "action"
        },
        {
          "id": 2,
          "name": "Adventure",
          "slug": "adventure"
        }
      ]
    },
    {
      "id": 15,
      "title": "Pirates of the Caribbean: Manga Edition",
      "slug": "pirates-caribbean-manga",
      "alternative_titles": ["カリブの海賊"],
      "description": "A manga adaptation of the famous pirate adventures...",
      "cover_image_url": "https://example.com/covers/pirates-caribbean.jpg",
      "status": "ongoing",
      "release_date": "2020-03-15T00:00:00Z",
      "country_of_origin": "Japan",
      "age_rating": "T",
      "total_views": 250000,
      "total_favorites": 3500,
      "last_chapter_uploaded_at": "2024-01-10T10:30:00Z",
      "uploader_id": 2,
      "created_at": "2020-03-15T00:00:00Z",
      "updated_at": "2024-01-10T10:30:00Z",
      "rank": 0.78,
      "_chapterCount": 45,
      "authors": [
        {
          "id": 15,
          "name": "Takeshi Yamamoto",
          "slug": "takeshi-yamamoto"
        }
      ],
      "genres": [
        {
          "id": 1,
          "name": "Action",
          "slug": "action"
        },
        {
          "id": 2,
          "name": "Adventure",
          "slug": "adventure"
        }
      ]
    }
  ],
  "totalPages": 5,
  "currentPage": 1,
  "totalComics": 87
}
```

#### Empty Results Response (200)
```json
{
  "comics": [],
  "totalPages": 0,
  "currentPage": 1,
  "totalComics": 0
}
```

## Search Algorithm

### Text Processing
1. **Query Normalization**: Trim whitespace and split into words
2. **Prefix Matching**: Each word gets `:*` suffix for partial matching
3. **Boolean Operators**: Words are joined with `&` (AND) operator
4. **Language Processing**: Uses PostgreSQL's English text search configuration

### Example Query Transformation
```
Input: "one piece adventure"
Processed: "one:* & piece:* & adventure:*"
```

### Ranking System
- Uses PostgreSQL's `ts_rank()` function
- Higher rank = better relevance match
- Results sorted by rank in descending order
- Rank values typically range from 0.0 to 1.0

### Search Fields
The search covers these fields with different weights:
- **Title** (highest weight)
- **Alternative titles** (high weight)
- **Description** (medium weight)

## Filtering

### Genre Filtering
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=ninja&genre=action"
```

### Status Filtering
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=romance&status=completed"
```

### Combined Filtering
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=school&genre=romance&status=ongoing"
```

## Response Data

### Manga Object in Search Results
Each manga in search results includes:
- **Basic Info**: ID, title, slug, description, cover image
- **Metadata**: Status, release date, country, age rating
- **Statistics**: Views, favorites, last update
- **Relationships**: Authors and genres
- **Search Rank**: Relevance score (0.0 - 1.0)
- **Chapter Count**: Total number of chapters

### Additional Fields
- `rank` - Search relevance score
- `_chapterCount` - Total chapters (computed field)

## Error Responses

### 400 Bad Request - Empty Query
```json
{
  "comics": [],
  "totalPages": 0,
  "currentPage": 1,
  "totalComics": 0
}
```

### 400 Bad Request - Invalid Parameters
```json
{
  "error": "Invalid parameters",
  "details": "Page must be a positive integer"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal Server Error"
}
```

## Performance Optimization

### Database Indexes
- Full-text search vector index on `search_vector` column
- Composite indexes on frequently filtered fields
- Optimized query execution plans

### Caching Strategy
- Search results cached for popular queries
- Cache invalidation on content updates
- CDN caching for static search suggestions

### Query Limits
- Maximum 100 results per page
- Query timeout protection
- Rate limiting for search requests

## Usage Examples

### Basic Search
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=naruto"
```

### Multi-word Search
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=attack+on+titan"
```

### Search with Filters
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=romance&genre=shoujo&status=completed&limit=20"
```

### Paginated Search
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=action&page=2&limit=15"
```

### Japanese Title Search
```bash
curl -X GET "https://truyentranhnuru.com/api/search?q=ワンピース"
```

## Search Tips

### For Users
- Use specific keywords for better results
- Try alternative spellings or titles
- Combine genre filters for targeted results
- Use partial words (search handles prefix matching)

### For Developers
- Implement debouncing for search input (300ms recommended)
- Cache frequent search queries
- Show search suggestions based on popular queries
- Implement search history for logged-in users

## Integration Examples

### Frontend Search Component
```javascript
const searchManga = async (query, filters = {}) => {
  const params = new URLSearchParams({
    q: query,
    page: filters.page || 1,
    limit: filters.limit || 20,
    ...(filters.genre && { genre: filters.genre }),
    ...(filters.status && { status: filters.status })
  });
  
  const response = await fetch(`/api/search?${params}`);
  return response.json();
};
```

### Search with Debouncing
```javascript
import { debounce } from 'lodash';

const debouncedSearch = debounce(async (query) => {
  if (query.length >= 2) {
    const results = await searchManga(query);
    setSearchResults(results);
  }
}, 300);
```
