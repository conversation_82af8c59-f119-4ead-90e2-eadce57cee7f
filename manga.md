# Manga API

## Overview

The Manga API provides endpoints for browsing, searching, and managing manga content including chapters, rankings, and view statistics.

## Endpoints

### Get Manga List

**GET** `/api/manga`

Retrieve a paginated list of manga with filtering and sorting options.

#### Query Parameters
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 100) - Items per page
- `sort` (string, default: "latest") - Sort order
  - `latest` - Recently updated
  - `popular` - Most viewed
  - `rating` - Highest rated
  - `title` - Alphabetical
  - `views` - Total views
- `status` (string) - Filter by status
  - `ongoing` - Currently publishing
  - `completed` - Finished series
  - `hiatus` - On break
- `genre` (string) - Filter by genre slug

#### Example Request
```bash
curl -X GET "https://truyentranhnuru.com/api/manga?sort=popular&limit=10&status=ongoing"
```

#### Success Response (200)
```json
{
  "comics": [
    {
      "id": 1,
      "title": "One Piece",
      "slug": "one-piece",
      "alternative_titles": ["ワンピース"],
      "description": "Epic pirate adventure manga...",
      "cover_image_url": "https://example.com/covers/one-piece.jpg",
      "status": "ongoing",
      "release_date": "1997-07-22T00:00:00Z",
      "country_of_origin": "Japan",
      "age_rating": "T",
      "total_views": 1500000,
      "total_favorites": 25000,
      "daily_views": 5000,
      "weekly_views": 35000,
      "monthly_views": 150000,
      "last_chapter_uploaded_at": "2024-01-15T10:30:00Z",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "_chapterCount": 1100,
      "authors": [
        {
          "id": 1,
          "name": "Eiichiro Oda",
          "slug": "eiichiro-oda"
        }
      ],
      "genres": [
        {
          "id": 1,
          "name": "Action",
          "slug": "action"
        },
        {
          "id": 2,
          "name": "Adventure", 
          "slug": "adventure"
        }
      ]
    }
  ],
  "totalPages": 150,
  "currentPage": 1,
  "total": 3000
}
```

### Get Manga Details

**GET** `/api/manga/{slug}`

Get detailed information about a specific manga.

#### Path Parameters
- `slug` (string) - Manga slug identifier

#### Example Request
```bash
curl -X GET "https://truyentranhnuru.com/api/manga/one-piece"
```

#### Success Response (200)
```json
{
  "id": 1,
  "title": "One Piece",
  "slug": "one-piece",
  "alternative_titles": ["ワンピース", "Wan Pīsu"],
  "description": "Gol D. Roger was known as the Pirate King...",
  "cover_image_url": "https://example.com/covers/one-piece.jpg",
  "status": "ongoing",
  "release_date": "1997-07-22T00:00:00Z",
  "country_of_origin": "Japan",
  "age_rating": "T",
  "total_views": 1500000,
  "total_favorites": 25000,
  "daily_views": 5000,
  "weekly_views": 35000,
  "monthly_views": 150000,
  "last_chapter_uploaded_at": "2024-01-15T10:30:00Z",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "authors": [
    {
      "id": 1,
      "name": "Eiichiro Oda",
      "slug": "eiichiro-oda",
      "bio": "Japanese manga artist...",
      "avatar_url": "https://example.com/authors/oda.jpg"
    }
  ],
  "genres": [
    {
      "id": 1,
      "name": "Action",
      "slug": "action",
      "description": "Action-packed adventures"
    }
  ],
  "publishers": [
    {
      "id": 1,
      "name": "Shueisha",
      "slug": "shueisha"
    }
  ],
  "_chapterCount": 1100,
  "_averageRating": 9.2,
  "_ratingCount": 15000,
  "_isFavorited": false
}
```

### Get Manga Chapters

**GET** `/api/manga/{slug}/chapters`

Get chapters for a specific manga with pagination.

#### Path Parameters
- `slug` (string) - Manga slug identifier

#### Query Parameters
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 50) - Items per page
- `all` (boolean, default: false) - Get all chapters without pagination

#### Example Request
```bash
curl -X GET "https://truyentranhnuru.com/api/manga/one-piece/chapters?page=1&limit=20"
```

#### Success Response (200)
```json
{
  "chapters": [
    {
      "id": 1100,
      "title": "The Final Island",
      "chapter_number": 1100,
      "volume_number": 108,
      "release_date": "2024-01-15T00:00:00Z",
      "page_count": 18,
      "comic_id": 1,
      "uploader_id": 1,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    },
    {
      "id": 1099,
      "title": "The Truth Revealed",
      "chapter_number": 1099,
      "volume_number": 108,
      "release_date": "2024-01-08T00:00:00Z",
      "page_count": 20,
      "comic_id": 1,
      "uploader_id": 1,
      "created_at": "2024-01-08T10:30:00Z",
      "updated_at": "2024-01-08T10:30:00Z"
    }
  ],
  "totalPages": 55,
  "currentPage": 1,
  "totalChapters": 1100
}
```

### Get Manga Rankings

**GET** `/api/manga/rankings`

Get manga rankings by different categories and time periods.

#### Query Parameters
- `category` (string, default: "views") - Ranking category
  - `views` - Most viewed
  - `favorites` - Most favorited
  - `rating` - Highest rated
  - `trending` - Trending manga
- `period` (string, default: "weekly") - Time period
  - `daily` - Last 24 hours
  - `weekly` - Last 7 days
  - `monthly` - Last 30 days
  - `all-time` - All time
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20) - Items per page

#### Example Request
```bash
curl -X GET "https://truyentranhnuru.com/api/manga/rankings?category=views&period=weekly&limit=10"
```

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "category": "views",
    "period": "weekly",
    "rankings": [
      {
        "id": 1,
        "title": "One Piece",
        "slug": "one-piece",
        "cover_image_url": "https://example.com/covers/one-piece.jpg",
        "daily_views": 5000,
        "weekly_views": 35000,
        "monthly_views": 150000,
        "total_views": 1500000,
        "total_favorites": 25000,
        "average_rating": 9.2,
        "rating_count": 15000,
        "rank": 1,
        "trend_direction": "up",
        "previous_rank": 2
      }
    ],
    "total": 100,
    "lastUpdated": "2024-01-15T12:00:00Z",
    "pagination": {
      "page": 1,
      "limit": 10,
      "totalPages": 10,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

### Record Manga View

**POST** `/api/manga/{slug}/view`

Record a view for a manga (used for analytics).

#### Path Parameters
- `slug` (string) - Manga slug identifier

#### Example Request
```bash
curl -X POST "https://truyentranhnuru.com/api/manga/one-piece/view"
```

#### Success Response (200)
```json
{
  "success": true,
  "message": "View recorded"
}
```

## Error Responses

### 404 Not Found
```json
{
  "error": "Manga not found"
}
```

### 400 Bad Request
```json
{
  "error": "Invalid parameters",
  "details": "Sort parameter must be one of: latest, popular, rating, title, views"
}
```

## Caching

Manga endpoints implement caching for better performance:

- **Manga List**: 5 minutes cache
- **Manga Details**: 10 minutes cache  
- **Rankings**: 1 hour cache
- **Chapters**: 30 minutes cache

## Usage Examples

### Get Popular Manga
```bash
curl -X GET "https://truyentranhnuru.com/api/manga?sort=popular&limit=10"
```

### Get Ongoing Action Manga
```bash
curl -X GET "https://truyentranhnuru.com/api/manga?status=ongoing&genre=action&page=1&limit=20"
```

### Get Weekly Rankings
```bash
curl -X GET "https://truyentranhnuru.com/api/manga/rankings?period=weekly&category=views&limit=10"
```

## Homepage Data Endpoint

**GET** `/api/home`

Get aggregated data for the homepage including popular manga, latest updates, rankings, and recent comments.

#### Query Parameters
- `page` (integer, default: 1) - Page number for latest manga

#### Success Response (200)
```json
{
  "popularManga": {
    "comics": [...],
    "total": 10
  },
  "latestManga": {
    "comics": [...],
    "totalPages": 150,
    "currentPage": 1,
    "total": 3000
  },
  "sidebarRankings": {
    "success": true,
    "data": {
      "rankings": [...]
    }
  },
  "recentComments": {
    "comments": [...],
    "total": 50
  }
}
```
