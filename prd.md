### K<PERSON> hoạch xây dựng trang đọc manga front-end với Next.js 15, <PERSON><PERSON><PERSON><PERSON>, Tailwind v4

Dưới đây là kế hoạch chi tiết để xây dựng một trang web đọc manga front-end sử dụng Next.js 15, Shadc<PERSON> (gi<PERSON> định là thư viện UI như @shadcn/ui), và Tailwind CSS v4, đáp ứng các yêu cầu đã nêu. Trang web sẽ được tối ưu hóa để tải nhanh thông qua Static Site Generation (SSG) và sử dụng các API đã cung cấp.

#### **Key Points**
- Trang web sẽ bao gồm bốn trang chính: Trang chủ, Chi tiết manga, Chi tiết chapter, và Trang tìm kiếm.
- Sử dụng Next.js 15 với SSG để đảm bảo tốc độ tả<PERSON> n<PERSON>h, kết hợp với Incremental Static Regeneration (ISR) cho các trang động.
- Shadcn và Tailwind CSS v4 sẽ được dùng để xây dựng giao diện đẹp và responsive.
- Lịch sử đọc và bookmark sẽ được lưu trữ trong localStorage, không yêu cầu backend cho người dùng.
- Dữ liệu sẽ được lấy từ các API RESTful đã cung cấp, như `/api/home`, `/api/manga`, `/api/chapters`, và `/api/search`.
- [manga.md](manga.md), [chapter.md](chapter.md), [search.md](search.md)
#### **Tổng quan**
Trang web sẽ được xây dựng với Next.js để tận dụng SSG, đảm bảo các trang tải ngay lập tức. Shadcn cung cấp các thành phần UI sẵn có, và Tailwind CSS giúp styling nhanh chóng. Các tính năng như lịch sử đọc và bookmark sẽ sử dụng localStorage để lưu trữ dữ liệu phía client.

#### **Cấu trúc trang web**
- **Trang chủ**: Hiển thị slider manga hot, danh sách manga mới cập nhật, sidebar với lịch sử đọc, bookmark, và top 10 manga hot.
- **Chi tiết manga**: Hiển thị thông tin manga, danh sách chapter, và nút bookmark.
- **Chi tiết chapter**: Hiển thị nội dung chapter với nav sticky cho điều hướng.
- **Trang tìm kiếm**: Form tìm kiếm với các bộ lọc như thể loại, bookmark, lượt xem.

#### **Cách triển khai**
- **SSG**: Sử dụng `getStaticProps` và `getStaticPaths` để pre-render các trang. Đối với các trang động (như chi tiết manga và chapter), sử dụng `fallback: true` để tạo trang khi cần.
- **API**: Tích hợp các endpoint như `/api/home`, `/api/manga/{slug}`, `/api/chapters/{id}`, và `/api/search`.
- **LocalStorage**: Quản lý lịch sử đọc và bookmark phía client.

---

### Báo cáo chi tiết về kế hoạch xây dựng trang đọc manga front-end

#### **1. Tổng quan dự án**
Mục tiêu là xây dựng một trang web đọc manga front-end với giao diện đẹp, tốc độ tải nhanh, và các tính năng như tìm kiếm, bookmark, và lịch sử đọc. Trang web sẽ sử dụng:
- **Next.js 15** ([Next.js Documentation](https://nextjs.org/docs)): Framework React hỗ trợ SSG và ISR.
- **Shadcn** (giả định là @shadcn/ui): Thư viện UI cung cấp các thành phần như Button, Input, Navbar.
- **Tailwind CSS v4** ([Tailwind CSS Documentation](https://tailwindcss.com/docs)): Hệ thống CSS utility-first để styling responsive.
- **API**: Các endpoint RESTful từ tài liệu đã cung cấp:
  - `/api/home`: Dữ liệu cho trang chủ (manga hot, mới cập nhật, xếp hạng).
  - `/api/manga`: Danh sách manga với phân trang và lọc.
  - `/api/manga/{slug}`: Chi tiết manga.
  - `/api/manga/{slug}/chapters`: Danh sách chapter của manga.
  - `/api/chapters/{id}`: Chi tiết chapter (bao gồm URL hình ảnh).
  - `/api/search`: Tìm kiếm manga với full-text search và bộ lọc.

#### **2. Cấu trúc dự án**
Cấu trúc thư mục dự án sẽ như sau:

| Thư mục       | Mô tả                                                                 |
|---------------|----------------------------------------------------------------------|
| `pages/`      | Chứa các trang chính: `index.js`, `manga/[slug].js`, `chapter/[id].js`, `search.js`. |
| `components/` | Các thành phần tái sử dụng: Header, Footer, Sidebar, ChapterReader.  |
| `styles/`     | Cấu hình Tailwind CSS và các file CSS tùy chỉnh nếu cần.             |
| `utils/`      | Hàm tiện ích: fetch API, quản lý localStorage.                       |

#### **3. Kế hoạch triển khai từng trang**

##### **3.1. Trang chủ (Home Page)**
- **Yêu cầu**:
  - Header với live search, thanh điều hướng, danh sách thể loại.
  - Sidebar hiển thị lịch sử đọc, bookmark (từ localStorage), và top 10 manga hot.
  - Slider hiển thị 12 manga hot.
  - Danh sách manga cập nhật mới nhất với phân trang.
  - Footer với thông tin cơ bản.
- **Dữ liệu**:
  - Fetch từ `/api/home` để lấy `popularManga` (slider), `latestManga` (danh sách mới), và `sidebarRankings` (top 10).
- **Triển khai**:
  - Sử dụng `getStaticProps` để fetch dữ liệu tại build time.
  - Header:
    - Live search: Gọi `/api/search` với debounce (300ms) khi người dùng gõ.
    - Thanh điều hướng: Liên kết đến Home, Search, v.v.
    - Thể loại: Hardcode hoặc lấy từ API nếu có endpoint.
  - Sidebar:
    - Lịch sử đọc và bookmark: Load từ localStorage bằng useEffect.
    - Top 10: Sử dụng `sidebarRankings`.
  - Slider: Hiển thị 12 manga từ `popularManga` với thư viện như `react-slick`.
  - Danh sách mới: Hiển thị `latestManga` với phân trang (query params `page` và `limit`).
  - Footer: Thông tin tĩnh (copyright, liên hệ).
- **SSG**: Sử dụng `getStaticProps` với `revalidate: 3600` (1 giờ) để cập nhật dữ liệu.

##### **3.2. Trang chi tiết manga (Manga Detail Page)**
- **Yêu cầu**:
  - Hiển thị thông tin manga (tên, mô tả, tác giả, thể loại, v.v.).
  - Danh sách chapter.
  - Nút bookmark lưu vào localStorage.
- **Dữ liệu**:
  - `/api/manga/{slug}`: Chi tiết manga.
  - `/api/manga/{slug}/chapters`: Danh sách chapter.
- **Triển khai**:
  - Dynamic route: `pages/manga/[slug].js`.
  - `getStaticPaths`: Fetch top 100 slug từ `/api/manga?limit=100&sort=popular`.
  - `getStaticProps`: Fetch chi tiết manga và chapter.
  - Giao diện:
    - Hiển thị thông tin manga (title, description, authors, genres).
    - Danh sách chapter với link đến `chapter/[id]`.
    - Nút bookmark: Lưu slug vào localStorage.
- **SSG**: `getStaticPaths` với `fallback: true`, `getStaticProps` với `revalidate: 3600`.

##### **3.3. Trang chi tiết chapter (Chapter Detail Page)**
- **Yêu cầu**:
  - Nav sticky khi cuộn với các icon: Home, Quay về manga, Form chọn chapter, Next/Prev chapter, Bookmark.
  - Hiển thị danh sách hình ảnh chapter.
- **Dữ liệu**:
  - `/api/chapters/{id}`: Chi tiết chapter (URL hình ảnh, prev/next chapter).
- **Triển khai**:
  - Dynamic route: `pages/chapter/[id].js`.
  - `getStaticPaths`: Trả về mảng rỗng với `fallback: true` do số lượng chapter lớn.
  - `getStaticProps`: Fetch chi tiết chapter.
  - Giao diện:
    - Nav sticky: Sử dụng CSS `position: sticky` hoặc thư viện `react-sticky`.
    - Form chọn chapter: Lấy danh sách chapter từ manga hoặc fetch lại.
    - Hiển thị hình ảnh: Sử dụng `next/image` để tối ưu.
    - Bookmark: Lưu chapter hoặc manga vào localStorage.
- **SSG**: `fallback: true`, `revalidate: 3600`.

##### **3.4. Trang tìm kiếm (Search Page)**
- **Yêu cầu**:
  - Form tìm kiếm với bộ lọc (thể loại, bookmark, lượt xem).
  - Kết quả tìm kiếm với phân trang.
- **Dữ liệu**:
  - `/api/search`: Tìm kiếm với query và bộ lọc (genre, status).
- **Triển khai**:
  - Trang static: `pages/search.js`.
  - Form tìm kiếm: Fetch `/api/search` client-side khi submit.
  - Bộ lọc:
    - Thể loại: Hardcode hoặc lấy từ API.
    - Bookmark: Kiểm tra localStorage.
    - Lượt xem: Sắp xếp theo `total_views` nếu API hỗ trợ.
  - Kết quả: Hiển thị với phân trang.
- **SSG**: Trang search là static, kết quả fetch client-side.

#### **4. Thành phần chung**
- **Header**:
  - Live search với debounce.
  - Thanh điều hướng và danh sách thể loại.
- **Footer**:
  - Thông tin tĩnh.
- **Sidebar**:
  - Lịch sử đọc, bookmark, top 10 hot.
- **ChapterReader**:
  - Hiển thị hình ảnh chapter với navigation.

#### **5. Quản lý dữ liệu người dùng**
- **LocalStorage**:
  - Lịch sử đọc: `localStorage.setItem('readingHistory', JSON.stringify([slug1, slug2]))`.
  - Bookmark: `localStorage.setItem('bookmarks', JSON.stringify([slug1, slug2]))`.
- **Client-side**:
  - Sử dụng React state và useEffect để đồng bộ dữ liệu.

#### **6. Tối ưu hóa**
- **SSG**: Pre-render trang chủ và top manga, sử dụng fallback cho trang động.
- **Lazy loading**: Sử dụng `next/image` cho hình ảnh.
- **Phân trang**: Áp dụng cho danh sách manga và tìm kiếm.
- **Caching**: Tận dụng caching của API (5-60 phút tùy endpoint).

#### **7. Các bước thực hiện**
1. **Khởi tạo dự án**:
   - Chạy `npx create-next-app@latest manga-reader --use-npm`.
   - Cài đặt Tailwind CSS và Shadcn.
2. **Tạo trang và thành phần**:
   - Theo cấu trúc đã nêu.
3. **Tích hợp API**:
   - Fetch dữ liệu trong `getStaticProps` hoặc client-side.
4. **Kiểm tra**:
   - Đảm bảo tốc độ tải nhanh và các tính năng hoạt động.

#### **8. Kết luận**
Kế hoạch này đảm bảo xây dựng một trang đọc manga front-end đáp ứng yêu cầu, với tốc độ tải nhanh nhờ SSG và giao diện đẹp nhờ Shadcn và Tailwind. Các tính năng như tìm kiếm, bookmark, và lịch sử đọc được triển khai hiệu quả.