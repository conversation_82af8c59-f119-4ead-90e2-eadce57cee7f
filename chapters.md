# Chapters API

## Overview

The Chapters API provides endpoints for accessing manga chapter content, managing chapter data, and tracking chapter views.

## Endpoints

### Get Chapter Details

**GET** `/api/chapters/{id}`

Get detailed information about a specific chapter including all pages.

#### Path Parameters
- `id` (string) - Chapter ID

#### Example Request
```bash
curl -X GET "https://truyentranhnuru.com/api/chapters/1099"
```

#### Success Response (200)
```json
{
  "id": 1099,
  "title": "The Truth Revealed",
  "chapter_number": 1099,
  "volume_number": 108,
  "release_date": "2024-01-08T00:00:00Z",
  "page_count": 20,
  "comic_id": 1,
  "uploader_id": 1,
  "created_at": "2024-01-08T10:30:00Z",
  "updated_at": "2024-01-08T10:30:00Z",
  "Comics": {
    "id": 1,
    "title": "One Piece",
    "slug": "one-piece",
    "cover_image_url": "https://example.com/covers/one-piece.jpg",
    "status": "ongoing"
  },
  "Users": {
    "id": 1,
    "username": "uploader",
    "role": "admin"
  },
  "Chapter_Pages": [
    {
      "id": 1,
      "chapter_id": 1099,
      "page_number": 1,
      "image_url": "https://example.com/chapters/1099/page-001.jpg",
      "width": 800,
      "height": 1200,
      "file_size": 245760,
      "created_at": "2024-01-08T10:30:00Z"
    },
    {
      "id": 2,
      "chapter_id": 1099,
      "page_number": 2,
      "image_url": "https://example.com/chapters/1099/page-002.jpg",
      "width": 800,
      "height": 1200,
      "file_size": 267890,
      "created_at": "2024-01-08T10:30:00Z"
    }
  ],
  "view_count": 15420,
  "previous_chapter": {
    "id": 1098,
    "title": "The Battle Continues",
    "chapter_number": 1098
  },
  "next_chapter": {
    "id": 1100,
    "title": "The Final Island",
    "chapter_number": 1100
  }
}
```

### Record Chapter View

**POST** `/api/chapters/{id}/view`

Record a view for a chapter (used for analytics and view counting).

#### Path Parameters
- `id` (string) - Chapter ID

#### Request Body (Optional)
```json
{
  "page_number": 5,
  "reading_time": 120
}
```

#### Example Request
```bash
curl -X POST "https://truyentranhnuru.com/api/chapters/1099/view" \
  -H "Content-Type: application/json" \
  -d '{
    "page_number": 5,
    "reading_time": 120
  }'
```

#### Success Response (200)
```json
{
  "success": true,
  "message": "View recorded",
  "view_count": 15421
}
```

### Report Chapter Issue

**POST** `/api/chapters/{id}/report`

Report an issue with a chapter (missing pages, wrong order, etc.).

**Authentication Required**: Yes

#### Path Parameters
- `id` (integer) - Chapter ID

#### Request Body
```json
{
  "reason": "missing_pages",
  "details": "Pages 5-7 are missing from this chapter"
}
```

#### Validation Rules
- `reason` (required): Issue category
  - `missing_pages` - Pages are missing
  - `wrong_order` - Pages in wrong order
  - `poor_quality` - Image quality issues
  - `wrong_chapter` - Wrong chapter content
  - `other` - Other issues
- `details` (optional): Additional description (max 500 characters)

#### Example Request
```bash
curl -X POST "https://truyentranhnuru.com/api/chapters/1099/report" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=<jwt-token>" \
  -d '{
    "reason": "missing_pages",
    "details": "Pages 5-7 are missing from this chapter"
  }'
```

#### Success Response (201)
```json
{
  "success": true,
  "message": "Chapter report submitted successfully",
  "report": {
    "id": 1,
    "user_id": 123,
    "chapter_id": 1099,
    "reason": "missing_pages",
    "details": "Pages 5-7 are missing from this chapter",
    "status": "pending",
    "created_at": "2024-01-15T12:00:00Z"
  }
}
```

## Chapter Data Structure

### Chapter Object
```json
{
  "id": 1099,
  "title": "The Truth Revealed",
  "chapter_number": 1099,
  "volume_number": 108,
  "release_date": "2024-01-08T00:00:00Z",
  "page_count": 20,
  "comic_id": 1,
  "uploader_id": 1,
  "created_at": "2024-01-08T10:30:00Z",
  "updated_at": "2024-01-08T10:30:00Z"
}
```

### Chapter Page Object
```json
{
  "id": 1,
  "chapter_id": 1099,
  "page_number": 1,
  "image_url": "https://example.com/chapters/1099/page-001.jpg",
  "width": 800,
  "height": 1200,
  "file_size": 245760,
  "created_at": "2024-01-08T10:30:00Z"
}
```

### Navigation Objects
```json
{
  "previous_chapter": {
    "id": 1098,
    "title": "The Battle Continues",
    "chapter_number": 1098
  },
  "next_chapter": {
    "id": 1100,
    "title": "The Final Island", 
    "chapter_number": 1100
  }
}
```

## Chapter Reports

### Report Reasons
- `missing_pages` - One or more pages are missing
- `wrong_order` - Pages are in incorrect order
- `poor_quality` - Image quality is poor or unreadable
- `wrong_chapter` - Content doesn't match chapter title/number
- `duplicate_pages` - Duplicate pages present
- `corrupted_images` - Images are corrupted or won't load
- `other` - Other issues not covered above

### Report Status
- `pending` - Report submitted, awaiting review
- `in_progress` - Report being investigated
- `resolved` - Issue has been fixed
- `dismissed` - Report was invalid or duplicate

### Report Limitations
- Users can only submit one report per chapter
- Reports are reviewed by moderators/admins
- Duplicate reports are automatically merged

## View Tracking

### View Analytics
Chapter views are tracked for:
- **Popular content identification**
- **Reading analytics**
- **Content performance metrics**
- **User engagement tracking**

### View Data
- **Anonymous views**: Tracked by IP/session
- **Authenticated views**: Linked to user account
- **Reading progress**: Page number and time spent
- **View timestamps**: For trend analysis

### Privacy
- IP addresses are hashed for privacy
- Personal data is not stored with view records
- Aggregated data only for analytics

## Error Responses

### 404 Not Found - Chapter Not Found
```json
{
  "error": "Chapter not found"
}
```

### 400 Bad Request - Invalid Report
```json
{
  "error": "Invalid report data",
  "details": "Reason is required and must be a valid option"
}
```

### 401 Unauthorized - Report Requires Auth
```json
{
  "error": "Unauthorized",
  "message": "Please log in to report issues"
}
```

### 409 Conflict - Duplicate Report
```json
{
  "error": "You have already reported this chapter",
  "details": "Only one report per chapter per user is allowed"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal Server Error"
}
```

## Usage Examples

### Get Chapter for Reading
```bash
curl -X GET "https://truyentranhnuru.com/api/chapters/1099"
```

### Record Chapter View
```bash
curl -X POST "https://truyentranhnuru.com/api/chapters/1099/view" \
  -H "Content-Type: application/json" \
  -d '{
    "page_number": 10,
    "reading_time": 300
  }'
```

### Report Missing Pages
```bash
curl -X POST "https://truyentranhnuru.com/api/chapters/1099/report" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=<jwt-token>" \
  -d '{
    "reason": "missing_pages",
    "details": "Pages 5-7 are missing"
  }'
```

### Report Poor Quality
```bash
curl -X POST "https://truyentranhnuru.com/api/chapters/1099/report" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=<jwt-token>" \
  -d '{
    "reason": "poor_quality",
    "details": "Images are very blurry and hard to read"
  }'
```

## Integration Examples

### Chapter Reader Component
```javascript
const ChapterReader = ({ chapterId }) => {
  const [chapter, setChapter] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  
  useEffect(() => {
    const loadChapter = async () => {
      try {
        const response = await fetch(`/api/chapters/${chapterId}`);
        const chapterData = await response.json();
        setChapter(chapterData);
        
        // Record view
        await fetch(`/api/chapters/${chapterId}/view`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ page_number: 1 })
        });
      } catch (error) {
        console.error('Failed to load chapter:', error);
      }
    };
    
    loadChapter();
  }, [chapterId]);
  
  const handlePageChange = async (pageNumber) => {
    setCurrentPage(pageNumber);
    
    // Record page view
    await fetch(`/api/chapters/${chapterId}/view`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ page_number: pageNumber })
    });
  };
  
  if (!chapter) return <div>Loading...</div>;
  
  return (
    <div className="chapter-reader">
      <h1>{chapter.title}</h1>
      <div className="pages">
        {chapter.Chapter_Pages.map((page) => (
          <img
            key={page.id}
            src={page.image_url}
            alt={`Page ${page.page_number}`}
            loading="lazy"
          />
        ))}
      </div>
      <div className="navigation">
        {chapter.previous_chapter && (
          <a href={`/chapters/${chapter.previous_chapter.id}`}>
            Previous: {chapter.previous_chapter.title}
          </a>
        )}
        {chapter.next_chapter && (
          <a href={`/chapters/${chapter.next_chapter.id}`}>
            Next: {chapter.next_chapter.title}
          </a>
        )}
      </div>
    </div>
  );
};
```

### Report Chapter Issues
```javascript
const reportChapter = async (chapterId, reason, details) => {
  try {
    const response = await fetch(`/api/chapters/${chapterId}/report`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason, details }),
    });
    
    const result = await response.json();
    
    if (result.success) {
      alert('Report submitted successfully. Thank you for helping improve the content!');
    } else {
      alert(result.error || 'Failed to submit report');
    }
  } catch (error) {
    console.error('Failed to submit report:', error);
    alert('Failed to submit report. Please try again.');
  }
};
```
